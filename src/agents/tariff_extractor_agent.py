import os
import json
import re
import sys
import os
from dotenv import load_dotenv
from openai import OpenAI

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

load_dotenv()

PERPLEXITY_API_URL = "https://api.perplexity.ai/chat/completions"
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")


# Initialize the OpenAI client
client = OpenAI(api_key=OPENAI_API_KEY)

def extract_tariff_info(country_tariff):
    try:
        completion = client.chat.completions.create(
            model="gpt-4o",  # Use appropriate model #gpt-4-turbo
            messages=[
                {
                    "role": "system",
                    "content": """You are a helpful assistant specializing in entity extraction. 
                    When provided with a data source containing Country,Export Volume,Standard Tariff,New Tariff, parse the Standard Tariff to extract applicable duty rate and applicable anti-dumping rate.
                    Return a JSON object with 1. country 2. export volume 3. duty, 4. anti-dumping and 5. New Tariff if applicable 6. Total Duty.
                    Return NA for elements you cant extract.
                    Format your response as valid JSON only with no additional text."""
                },
                {
                    "role": "user",
                    "content": f"As instructed, extract tariff speciifcs from the following source {country_tariff}"
                }
            ],
            response_format={"type": "json_object"}  # Enforce JSON response
        )

        response = completion.choices[0].message.content
        return json.loads(response)
    
    except Exception as e:
        print(f"Error fetching chemical information: {e}")
        raise

def extract_tariff_info_structured_assist_openAI(country_tariff):
    json_schema={
        'hts_no': '3907.30.00.00', 
        'country': 'Singapore', 
        'duty': '3.5%', 
        'footer_duty': '10%',
        'New Tariff': '0%',
        'Anti-Dumping Duty': '2.5%',
        'Countervailing Duty': '1.5%',
        'Total Duty': '17.5%', #Example: 3.5 + 10 + 0 + 2.5 + 1.5 = 17.5
        'Proposed Tariff': '5%',
        'Effective Date': '2024-07-01'
    }
    prompt=f"""
        You are provided with a data source containing Country, Standard Tariff, New Tariff, Proposed Tariff, Anti-Dumping Duty and Countervailing Duty.
        1. Parse the Standard Tariff to extract applicable duty rate and applicable footer_duty rate.
        2. Parse the New Tariff to get the applicable new tariff.
        3. Parse the Proposed Tariff to extract applicable proposed tariff and the effective date.

        Return a JSON object with:
        1. hts_no 
        2. country 
        3. duty
        4. footer_duty
        5. New Tariff (if applicable) 
        6. Anti-Dumping Duty
        7. Countervailing Duty
        8. Total Duty = duty + footer_duty + New Tariff + Anti-Dumping Duty + Countervailing Duty
        9. Proposed Tariff (if applicable)
        10. Effective Date

        Don't put text in the duty/tariff feild. Use only numbers eg 0% instead of Free or NA.
        For all other feilds, return NA for elements you cant extract.
        Format your response as valid JSON only with no additional text.
        As instructed, extract tariff specifics from the following source: {country_tariff}, don't use example values.  
    """

    client = OpenAI()
    response = client.chat.completions.create(
                model="gpt-4o",#
                response_format={ "type": "json_object" },
                messages=[
                    {"role": "system", "content": "Provide valid JSON output. The data schema should be like this:"+json.dumps(json_schema)},
                    {"role": "user",
                        "content": [
                            {
                            "type": "text",
                            "text": f"{prompt}"
                            },
                        ],
                    }
                ],
                #max_tokens=self.max_tokens, #wih gpt-4 better to not restrict else it leaves the chat incomplete.#also max-toekns include prompt tokens as well
            )    

    response_content = response.choices[0].message.content
    return response_content

# Example usage
if __name__ == "__main__":
    tariff_blob="Country: China,Export Volume:3432870,Standard Tariff:    {'country': 'CN', 'duty':10%},New Tariff:Trump's recent announcement enforces 20% addit..."
    try:
        #result = extract_tariff_info(tariff_blob)
        result = extract_tariff_info_structured_assist_openAI(tariff_blob)
        print(json.dumps(result, indent=2))
        
        # Example output:
        # {
        #   "product_name": "Sodium Hydroxide",
        #   "hts_number": "2815110000",
        #   "cas_number": "1310-73-2",
        #   "product_family": "Inorganic Base/Alkali",
        #   "product_application": "pH regulation, cleaning agent, chemical manufacturing"
        # }
        
    except Exception as e:
        print(f"Failed to get chemical information: {e}")
